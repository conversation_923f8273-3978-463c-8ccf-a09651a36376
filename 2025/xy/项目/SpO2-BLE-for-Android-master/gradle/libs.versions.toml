[versions]
agp = "8.9.1"
githubFastble = "2.4.0"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"
appcompat = "1.6.1"
material = "1.10.0"
activity = "1.8.1"
constraintlayout = "2.1.4"
xxpermissions = "20.0"

[libraries]
github-fastble = { module = "com.github.Jasonchenlijian:FastBle", version.ref = "githubFastble" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
xxpermissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxpermissions" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }

